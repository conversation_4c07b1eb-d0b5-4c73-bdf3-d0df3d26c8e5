<?php

namespace App\Http\Controllers;

use App\Models\CalonSiswa;
use App\Models\OrangTua;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrangTuaController extends Controller
{
    public function index()
    {
        return view('calon-siswa.data-orangtua');
    }

    public function create()
    {
        return view('calon-siswa.data-orangtua');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'nama_ayah' => 'required',
            'pekerjaan_ayah' => 'required',
            'pendidikan_ayah' => 'required',
            'no_hp_ayah' => 'required',
            'nama_ibu' => 'required',
            'pekerjaan_ibu' => 'required',
            'pendidikan_ibu' => 'required',
            'no_hp_ibu' => 'required',
        ]);

        $akun_id = Auth::id();
        $calonSiswa = CalonSiswa::where('akun_id', $akun_id)->first();

        OrangTua::create([
            'calon_siswa_id' => $calonSiswa->id,
            'nama_ayah' => $validatedData['nama_ayah'],
            'pekerjaan_ayah' => $validatedData['pekerjaan_ayah'],
            'pendidikan_ayah' => $validatedData['pendidikan_ayah'],
            'no_hp_ayah' => $validatedData['no_hp_ayah'],
            'nama_ibu' => $validatedData['nama_ibu'],
            'pekerjaan_ibu' => $validatedData['pekerjaan_ibu'],
            'pendidikan_ibu' => $validatedData['pendidikan_ibu'],
            'no_hp_ibu' => $validatedData['no_hp_ibu'],
        ]);

        return redirect()->route('calon-siswa.upload-berkas')->with('success', 'Data orang tua berhasil disimpan.');
    }

    public function edit(OrangTua $orangtua)
    {
        return view('orangtua.edit', compact('orangtua'));
    }

    public function update(Request $request, OrangTua $orangtua)
    {
        $validatedData = $request->validate([
            'nama_ayah' => 'required',
            'pekerjaan_ayah' => 'required',
            'pendidikan_ayah' => 'required',
            'no_hp_ayah' => 'required',
            'nama_ibu' => 'required',
            'pekerjaan_ibu' => 'required',
            'pendidikan_ibu' => 'required',
            'no_hp_ibu' => 'required',
        ]);

        $orangtua->update([
            'nama_ayah' => $validatedData['nama_ayah'],
            'pekerjaan_ayah' => $validatedData['pekerjaan_ayah'],
            'pendidikan_ayah' => $validatedData['pendidikan_ayah'],
            'no_hp_ayah' => $validatedData['no_hp_ayah'],
            'nama_ibu' => $validatedData['nama_ibu'],
            'pekerjaan_ibu' => $validatedData['pekerjaan_ibu'],
            'pendidikan_ibu' => $validatedData['pendidikan_ibu'],
            'no_hp_ibu' => $validatedData['no_hp_ibu'],
        ]);

        return redirect()->route('orangtua.index')->with('success', 'Data orang tua berhasil diperbarui.');
    }

    public function destroy(OrangTua $orangtua)
    {
        $orangtua->delete();
        return redirect()->route('orangtua.index')->with('success', 'Data orang tua berhasil dihapus.');
    }
}
