<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Pengumuman - Dashboard Operator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-gray-50 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-200 ease-in-out"
        id="sidebar">
        <div class="flex items-center justify-center h-16 bg-blue-600">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-blue-600"></i>
                </div>
                <span class="text-white font-bold text-lg">MA Al-Muhsinin</span>
            </div>
        </div>

        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="{{ route('operator.dashboard') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-home mr-3"></i>
                    Dashboard
                </a>
                <a href="{{ route('pengumuman.index') }}"
                    class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 rounded-lg">
                    <i class="fas fa-bullhorn mr-3"></i>
                    Pengumuman
                </a>
                <a href="{{ route('operator.verifikasi') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-check-circle mr-3"></i>
                    Verifikasi Berkas
                </a>
                <a href="{{ route('operator.kelulusan') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-user-graduate mr-3"></i>
                    Kelulusan
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex justify-between items-center px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-600 hover:text-gray-900 mr-4" onclick="toggleSidebar()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900">Kelola Pengumuman</h1>
                </div>

                <div class="flex items-center space-x-4">
                    <button onclick="openCreateModal()"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Buat Pengumuman
                    </button>
                </div>
            </div>
        </header>

        <!-- Content -->
        <main class="p-6">
            <!-- Filter & Search -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Cari pengumuman..."
                                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <select
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Semua Status</option>
                            <option value="aktif">Aktif</option>
                            <option value="nonaktif">Non-aktif</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Pengumuman List -->
            <div class="space-y-4">
                <!-- Pengumuman Item 1 -->
                @foreach ($pengumuman as $item)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $item->judul }}</h3>
                                    <span
                                        class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">{{ $item->status }}</span>
                                </div>
                                <p class="text-gray-600 mb-3">{{ $item->isi }}</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span><i
                                            class="fas fa-calendar mr-1"></i>{{ date('d F Y', strtotime($item->created_at)) }}
                                    </span>
                                    <span><i class="fas fa-eye mr-1"></i>1,245 views</span>
                                    <span><i class="fas fa-user mr-1"></i>Operator Sekolah</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <button onclick="editPengumuman({{ $item->id }})"
                                    class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deletePengumuman(1)"
                                    class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach


            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between mt-6">
                <div class="text-sm text-gray-700">
                    Menampilkan <span class="font-medium">1</span> sampai <span class="font-medium">3</span> dari <span
                        class="font-medium">3</span> pengumuman
                </div>
                <div class="flex items-center space-x-2">
                    <button
                        class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
                        disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-2 text-white bg-blue-600 border border-blue-600 rounded-lg">1</button>
                    <button
                        class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
                        disabled>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Create/Edit Modal -->
    <div id="pengumumanModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeModal()"></div>

            <div
                class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Buat Pengumuman Baru</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="pengumumanForm" method="POST" class="space-y-6" action="{{ route('pengumuman.store') }}">
                    @csrf
                    <div>
                        <label for="judul" class="block text-sm font-medium text-gray-700 mb-2">Judul Pengumuman</label>
                        <input type="text" id="judul" name="judul" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Masukkan judul pengumuman">
                    </div>

                    <div>
                        <label for="isi" class="block text-sm font-medium text-gray-700 mb-2">Isi Pengumuman</label>
                        <textarea id="isi" name="isi" rows="6" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                            placeholder="Tulis isi pengumuman..."></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="status" name="status" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="draft">Draft</option>
                                <option value="aktif">Aktif</option>
                                <option value="nonaktif">Non-aktif</option>
                            </select>
                        </div>

                        <div>
                            <label for="tanggal_publikasi" class="block text-sm font-medium text-gray-700 mb-2">Tanggal
                                Publikasi</label>
                            <input type="datetime-local" id="tanggal_publikasi" name="tanggal_publikasi"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeModal()"
                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            Batal
                        </button>
                        <button type="submit"
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        }

        function openCreateModal() {
            document.getElementById('modalTitle').textContent = 'Buat Pengumuman Baru';
            const form = document.getElementById('pengumumanForm');
            form.reset();
            form.action = `{{ route('pengumuman.store') }}`;

            // Hapus method PUT jika sebelumnya edit
            const oldMethod = document.getElementById('methodField');
            if (oldMethod) oldMethod.remove();

            document.getElementById('pengumumanModal').classList.remove('hidden');
        }

        function editPengumuman(id) {
            // Fetch data dari server
            fetch(`{{ url('operator/pengumuman') }}/${id}/edit`)
                .then(response => response.json())
                .then(data => {
                    if (data) {
                        document.getElementById('modalTitle').textContent = 'Edit Pengumuman';
                        document.getElementById('judul').value = data.judul;
                        document.getElementById('isi').value = data.isi;
                        document.getElementById('status').value = data.status;

                        if (data.tanggal_publikasi) {
                            document.getElementById('tanggal_publikasi').value = data.tanggal_publikasi;
                        }

                        const form = document.getElementById('pengumumanForm');
                        form.action = `{{ url('operator/pengumuman') }}/${id}`;

                        // Hapus _method sebelumnya jika ada
                        const oldMethod = document.getElementById('methodField');
                        if (oldMethod) oldMethod.remove();

                        // Tambahkan method PUT untuk update
                        const methodInput = document.createElement('input');
                        methodInput.type = 'hidden';
                        methodInput.name = '_method';
                        methodInput.value = 'PUT';
                        methodInput.id = 'methodField';
                        form.appendChild(methodInput);

                        document.getElementById('pengumumanModal').classList.remove('hidden');
                    } else {
                        alert('Data pengumuman tidak ditemukan!');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Gagal memuat data pengumuman!');
                });
        }

        function deletePengumuman(id) {
            if (confirm('Apakah Anda yakin ingin menghapus pengumuman ini?')) {
                // Kirim request DELETE ke server
                fetch(`{{ url('operator/pengumuman') }}/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            // Refresh halaman untuk melihat perubahan
                            location.reload();
                        } else {
                            alert('Gagal menghapus pengumuman!');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Terjadi kesalahan saat menghapus pengumuman!');
                    });
            }
        }

        function closeModal() {
            document.getElementById('pengumumanModal').classList.add('hidden');
        }

        // Form submission handler
        document.getElementById('pengumumanForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(this);
            const isEdit = document.getElementById('methodField') !== null;
            const url = this.action;
            const method = isEdit ? 'PUT' : 'POST';

            // Konversi FormData ke object untuk JSON
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // Kirim request ke server
            fetch(url, {
                method: 'POST', // Selalu POST, method override lewat _method
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        closeModal();
                        // Refresh halaman untuk melihat perubahan
                        location.reload();
                    } else {
                        alert('Terjadi kesalahan saat menyimpan pengumuman!');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat menyimpan pengumuman!');
                });
        });
    </script>
</body>

</html>