<?php

namespace App\Http\Controllers;

use App\Models\Berkas;
use App\Models\CalonSiswa;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BerkasController extends Controller
{
    public function create()
    {
        return view('calon-siswa.upload-berkas');
    }

    public function store(Request $request)
    {
        $request->validate([
            'foto_siswa' => 'required|image|max:2048',
            'kartu_keluarga' => 'required|mimes:pdf,jpg,jpeg,png|max:2048',
            'akta_kelahiran' => 'required|mimes:pdf,jpg,jpeg,png|max:2048',
            'ijazah_smp' => 'required|mimes:pdf,jpg,jpeg,png|max:2048',
        ]);

        $fields = [
            'foto_siswa' => 'Pas Foto 3x4',
            'kartu_keluarga' => 'Kartu Keluarga',
            'akta_kelahiran' => 'Akta <PERSON>n',
            'ijazah_smp' => 'Ijazah SMP/Sederajat/SKL',
        ];

        $akun_id = Auth::id();
        $calonSiswa = CalonSiswa::where('akun_id', $akun_id)->first();

        foreach ($fields as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                $filename = time() . '_' . $field . '_' . $file->getClientOriginalName();
                $file->move(public_path('uploads'), $filename);

                Berkas::create([
                    'calon_siswa_id' => $calonSiswa->id,
                    'jenis_berkas' => $field,
                    'nama_file' => $filename,
                    'file_path' => 'uploads/' . $filename,
                    'status' => 'belum_diverifikasi',
                    'catatan' => '',
                    'uploaded_at' => now(),
                ]);
            }
        }

        return redirect()->back()->with('success', 'Semua file berhasil diunggah.');
    }


    public function edit($id)
    {
        return view('berkas.edit');
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'file' => 'required|mimes:pdf|max:2048',
        ]);


    }

}
