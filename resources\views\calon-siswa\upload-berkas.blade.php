<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Berkas - MA Al-Muhsinin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo & Title -->
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">MA Al-Muhsinin</h1>
                        <p class="text-sm text-gray-500">Upload Berkas</p>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name ?? 'Calon Siswa' }}</p>
                        <p class="text-xs text-gray-500">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-blue-600 text-sm"></i>
                    </div>
                    <button onclick="logout()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Progress Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Progress Pendaftaran</h3>
                <span class="text-sm text-blue-600 font-medium">3 dari 3 langkah</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
            </div>
            <div class="flex justify-between mt-2 text-sm">
                <span class="text-green-600 font-medium">✓ Data Pribadi</span>
                <span class="text-green-600 font-medium">✓ Data Orang Tua</span>
                <span class="text-blue-600 font-medium">Upload Berkas</span>
            </div>
        </div>

        <!-- Info Card -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-600"></i>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-blue-900 mb-2">Persyaratan Upload Berkas</h4>
                    <ul class="text-blue-800 text-sm space-y-1">
                        <li>• Format file: PDF, JPG, JPEG, PNG</li>
                        <li>• Ukuran maksimal: 2MB per file</li>
                        <li>• Pastikan file dapat dibaca dengan jelas</li>
                        <li>• Semua berkas wajib diupload</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center space-x-3 mb-6">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cloud-upload-alt text-blue-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900">Upload Berkas Pendaftaran</h3>
            </div>

            @if ($errors->any())
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                        <span class="text-red-700 font-medium">Terdapat kesalahan:</span>
                    </div>
                    <ul class="text-red-600 text-sm list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span class="text-green-700 font-medium">{{ session('success') }}</span>
                    </div>
                </div>
            @endif

            <form method="POST" action="{{ route('upload-berkas.store') }}" enctype="multipart/form-data"
                class="space-y-6">
                @csrf

                <!-- Berkas yang diperlukan -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                    <!-- Foto Siswa -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Pas Foto 3x4 <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('foto_siswa').click()">
                            <i class="fas fa-camera text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload foto</p>
                            <p class="text-gray-400 text-xs mt-1">JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="foto_siswa" name="foto_siswa" accept="image/*" class="sr-only" required
                            onchange="previewFile(this, 'preview_foto_siswa')">
                        <div id="preview_foto_siswa" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Kartu Keluarga -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Kartu Keluarga <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('kartu_keluarga').click()">
                            <i class="fas fa-id-card text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload KK</p>
                            <p class="text-gray-400 text-xs mt-1">PDF, JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="kartu_keluarga" name="kartu_keluarga" accept=".pdf,image/*"
                            class="hidden" required onchange="previewFile(this, 'preview_kartu_keluarga')">
                        <div id="preview_kartu_keluarga" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Akta Kelahiran -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Akta Kelahiran <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('akta_kelahiran').click()">
                            <i class="fas fa-certificate text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload akta</p>
                            <p class="text-gray-400 text-xs mt-1">PDF, JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="akta_kelahiran" name="akta_kelahiran" accept=".pdf,image/*"
                            class="hidden" required onchange="previewFile(this, 'preview_akta_kelahiran')">
                        <div id="preview_akta_kelahiran" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Ijazah SMP -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Ijazah SMP/Sederajat <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('ijazah_smp').click()">
                            <i class="fas fa-scroll text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload ijazah</p>
                            <p class="text-gray-400 text-xs mt-1">PDF, JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="ijazah_smp" name="ijazah_smp" accept=".pdf,image/*" class="hidden"
                            required onchange="previewFile(this, 'preview_ijazah_smp')">
                        <div id="preview_ijazah_smp" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>

                    <!-- SKHUN -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            SKHUN SMP/Sederajat <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('skhun').click()">
                            <i class="fas fa-file-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload SKHUN</p>
                            <p class="text-gray-400 text-xs mt-1">PDF, JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="skhun" name="skhun" accept=".pdf,image/*" class="hidden" required
                            onchange="previewFile(this, 'preview_skhun')">
                        <div id="preview_skhun" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Raport -->
                    <div class="upload-item">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Raport Semester 5 <span class="text-red-500">*</span>
                        </label>
                        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                            onclick="document.getElementById('raport').click()">
                            <i class="fas fa-book-open text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600 text-sm">Klik untuk upload raport</p>
                            <p class="text-gray-400 text-xs mt-1">PDF, JPG, PNG (Max: 2MB)</p>
                        </div>
                        <input type="file" id="raport" name="raport" accept=".pdf,image/*" class="hidden" required
                            onchange="previewFile(this, 'preview_raport')">
                        <div id="preview_raport" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 text-sm text-green-600">
                                <i class="fas fa-check-circle"></i>
                                <span class="file-name"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <a href="{{ route('calon-siswa.data-orangtua') }}"
                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>Kembali
                    </a>

                    <div class="flex space-x-4">
                        <button type="button" onclick="resetForm()"
                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-undo mr-2"></i>Reset
                        </button>
                        <button type="submit"
                            class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center">
                            <i class="fas fa-check mr-2"></i>Selesaikan Pendaftaran
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <script>
        function logout() {
            if (confirm('Apakah Anda yakin ingin keluar?')) {
                window.location.href = '/logout';
            }
        }

        function resetForm() {
            if (confirm('Apakah Anda yakin ingin mereset semua file?')) {
                document.querySelector('form').reset();
                // Hide all previews
                document.querySelectorAll('[id^="preview_"]').forEach(preview => {
                    preview.classList.add('hidden');
                });
                // Reset upload areas
                document.querySelectorAll('.upload-area').forEach(area => {
                    area.classList.remove('border-green-400', 'bg-green-50');
                    area.classList.add('border-gray-300');
                });
            }
        }

        function previewFile(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);
            const uploadArea = input.parentElement.querySelector('.upload-area');

            if (file) {
                // Check file size (2MB = 2 * 1024 * 1024 bytes)
                if (file.size > 2 * 1024 * 1024) {
                    alert('Ukuran file terlalu besar! Maksimal 2MB.');
                    input.value = '';
                    return;
                }

                // Show preview
                preview.classList.remove('hidden');
                preview.querySelector('.file-name').textContent = file.name;

                // Update upload area style
                uploadArea.classList.remove('border-gray-300');
                uploadArea.classList.add('border-green-400', 'bg-green-50');
            }
        }

        // Drag and drop functionality
        document.querySelectorAll('.upload-area').forEach(area => {
            area.addEventListener('dragover', function (e) {
                e.preventDefault();
                this.classList.add('border-blue-400', 'bg-blue-50');
            });

            area.addEventListener('dragleave', function (e) {
                e.preventDefault();
                this.classList.remove('border-blue-400', 'bg-blue-50');
            });

            area.addEventListener('drop', function (e) {
                e.preventDefault();
                this.classList.remove('border-blue-400', 'bg-blue-50');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = this.parentElement.querySelector('input[type="file"]');
                    input.files = files;

                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });
        });
    </script>
</body>

</html>