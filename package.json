{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"autoprefixer": "^10.4.21", "axios": "^1.10.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}}