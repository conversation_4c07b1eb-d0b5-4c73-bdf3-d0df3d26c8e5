<?php

use App\Http\Controllers\AkunController;
use App\Http\Controllers\BerkasController;
use App\Http\Controllers\CalonSiswaController;
use App\Http\Controllers\OrangTuaController;
use App\Http\Controllers\PengumumanController;
use App\Http\Controllers\SendOtpMailController;
use App\Mail\SendOtpMail;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Auth routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');
Route::post('/login', [AkunController::class, 'login'])->name('login.post');

// Register routes
Route::get('/register', [AkunController::class, 'create'])->name('register');
Route::post('/register', [AkunController::class, 'storeAkunCalonSiswa'])->name('register.post');

// OTP Verification routes
Route::get('/verify-otp', [SendOtpMailController::class, 'index'])->name('verify-otp');
Route::post('/verify-otp', [SendOtpMailController::class, 'verifyOtp'])->name('verify-otp.post');

Route::get('/test-email', function () {
    Mail::to('<EMAIL>')->send(new SendOtpMail('123456'));
    return 'Email berhasil dikirim (cek Mailtrap)';
});

// Dashboard Calon Siswa routes
Route::get('/dashboard/calon-siswa/data-diri', [CalonSiswaController::class, 'create'])->name('calon-siswa.data-diri');
Route::post('/dashboard/calon-siswa/data-diri', [CalonSiswaController::class, 'store'])->name('calon-siswa.store');

// Data Orang Tua routes
Route::get('/dashboard/calon-siswa/data-orangtua', [OrangTuaController::class, 'create'])->name('calon-siswa.data-orangtua');
Route::post('/dashboard/calon-siswa/data-orangtua', [OrangTuaController::class, 'store'])->name('orangtua.store');

// Upload Berkas routes
Route::get('/dashboard/calon-siswa/upload-berkas', [BerkasController::class, 'create'])->name('calon-siswa.upload-berkas');
Route::post('/dashboard/calon-siswa/upload-berkas', [BerkasController::class, 'store'])->name('upload-berkas.store');

// Operator Dashboard routes
Route::prefix('operator')->group(function () {
    Route::get('/dashboard', function () {
        return view('operator.dashboard');
    })->name('operator.dashboard');

    Route::get('/pengumuman', [PengumumanController::class, 'index'])->name('pengumuman.index');
    Route::post('/pengumuman', [PengumumanController::class, 'store'])->name('pengumuman.store');
    Route::get('/pengumuman/{id}', [PengumumanController::class, 'edit'])->name('pengumuman.edit');
    Route::put('/pengumuman/{id}', [PengumumanController::class, 'update'])->name('pengumuman.update');

    Route::get('/verifikasi', function () {
        return view('operator.verifikasi');
    })->name('operator.verifikasi');

    Route::get('/kelulusan', function () {
        return view('operator.kelulusan');
    })->name('operator.kelulusan');
});