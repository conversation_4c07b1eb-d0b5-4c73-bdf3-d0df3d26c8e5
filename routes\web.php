<?php

use App\Http\Controllers\AkunController;
use App\Http\Controllers\BerkasController;
use App\Http\Controllers\CalonSiswaController;
use App\Http\Controllers\OrangTuaController;
use App\Http\Controllers\PengumumanController;
use App\Http\Controllers\SendOtpMailController;
use App\Mail\SendOtpMail;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Auth routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');
Route::post('/login', [AkunController::class, 'login'])->name('login.post');

// Register routes
Route::get('/register', [AkunController::class, 'create'])->name('register');
Route::post('/register', [AkunController::class, 'storeAkunCalonSiswa'])->name('register.post');

// OTP Verification routes
Route::get('/verify-otp', [SendOtpMailController::class, 'index'])->name('verify-otp');
Route::post('/verify-otp', [SendOtpMailController::class, 'verifyOtp'])->name('verify-otp.post');

Route::get('/test-email', function () {
    Mail::to('<EMAIL>')->send(new SendOtpMail('123456'));
    return 'Email berhasil dikirim (cek Mailtrap)';
});

// Dashboard Calon Siswa routes
Route::get('/dashboard/calon-siswa/data-diri', [CalonSiswaController::class, 'create'])->name('calon-siswa.data-diri');
Route::post('/dashboard/calon-siswa/data-diri', [CalonSiswaController::class, 'store'])->name('calon-siswa.store');

// Data Orang Tua routes
Route::get('/dashboard/calon-siswa/data-orangtua', [OrangTuaController::class, 'create'])->name('calon-siswa.data-orangtua');
Route::post('/dashboard/calon-siswa/data-orangtua', [OrangTuaController::class, 'store'])->name('orangtua.store');

// Upload Berkas routes
Route::get('/dashboard/calon-siswa/upload-berkas', [BerkasController::class, 'create'])->name('calon-siswa.upload-berkas');
Route::post('/dashboard/calon-siswa/upload-berkas', [BerkasController::class, 'store'])->name('upload-berkas.store');

// Operator Dashboard routes
Route::prefix('operator')->group(function () {
    Route::get('/dashboard', function () {
        return view('operator.dashboard');
    })->name('operator.dashboard');

    Route::get('/pengumuman', function () {
        return view('operator.pengumuman');
    })->name('operator.pengumuman');

    Route::post('/pengumuman', function () {
        // Create pengumuman logic
        return response()->json(['success' => true, 'message' => 'Pengumuman berhasil dibuat!']);
    })->name('operator.pengumuman.store');

    Route::get('/pengumuman/{id}/edit', function ($id) {
        // Get pengumuman data for edit
        $dummyData = [
            1 => [
                'id' => 1,
                'judul' => 'Pengumuman Hasil Seleksi PPDB 2024',
                'isi' => 'Hasil seleksi PPDB MA Al-Muhsinin tahun ajaran 2024/2025 telah diumumkan. Silakan cek status kelulusan Anda melalui sistem...',
                'status' => 'aktif',
                'tanggal_publikasi' => '2024-07-15T10:00'
            ],
            2 => [
                'id' => 2,
                'judul' => 'Jadwal Daftar Ulang Siswa Baru',
                'isi' => 'Bagi calon siswa yang dinyatakan lulus, silakan melakukan daftar ulang sesuai jadwal yang telah ditentukan...',
                'status' => 'draft',
                'tanggal_publikasi' => '2024-07-14T09:00'
            ],
            3 => [
                'id' => 3,
                'judul' => 'Perpanjangan Waktu Pendaftaran',
                'isi' => 'Waktu pendaftaran PPDB MA Al-Muhsinin diperpanjang hingga tanggal 20 Juni 2024. Bagi yang belum mendaftar...',
                'status' => 'nonaktif',
                'tanggal_publikasi' => '2024-06-10T08:00'
            ]
        ];

        return response()->json($dummyData[$id] ?? null);
    })->name('operator.pengumuman.edit');

    Route::put('/pengumuman/{id}', function ($id) {
        // Update pengumuman logic
        return response()->json(['success' => true, 'message' => 'Pengumuman berhasil diperbarui!']);
    })->name('operator.pengumuman.update');

    Route::delete('/pengumuman/{id}', function ($id) {
        // Delete pengumuman logic
        return response()->json(['success' => true, 'message' => 'Pengumuman berhasil dihapus!']);
    })->name('operator.pengumuman.delete');

    Route::get('/verifikasi', function () {
        return view('operator.verifikasi');
    })->name('operator.verifikasi');

    Route::get('/kelulusan', function () {
        return view('operator.kelulusan');
    })->name('operator.kelulusan');
});