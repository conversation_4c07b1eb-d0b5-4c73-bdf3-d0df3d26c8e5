<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Veri<PERSON><PERSON><PERSON> - Dashboard Operator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-gray-50 font-sans">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-200 ease-in-out"
        id="sidebar">
        <div class="flex items-center justify-center h-16 bg-blue-600">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-blue-600"></i>
                </div>
                <span class="text-white font-bold text-lg">MA Al-Muhsinin</span>
            </div>
        </div>

        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="{{ route('operator.dashboard') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-home mr-3"></i>
                    Dashboard
                </a>
                <a href="{{ route('pengumuman.index') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-bullhorn mr-3"></i>
                    Pengumuman
                </a>
                <a href="{{ route('operator.verifikasi') }}"
                    class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 rounded-lg">
                    <i class="fas fa-check-circle mr-3"></i>
                    Verifikasi Berkas
                </a>
                <a href="{{ route('operator.kelulusan') }}"
                    class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <i class="fas fa-user-graduate mr-3"></i>
                    Kelulusan
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex justify-between items-center px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-600 hover:text-gray-900 mr-4" onclick="toggleSidebar()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900">Verifikasi Berkas</h1>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        <span class="font-medium">48</span> berkas menunggu verifikasi
                    </div>
                </div>
            </div>
        </header>

        <!-- Content -->
        <main class="p-6">
            <!-- Filter & Search -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Cari nama siswa..."
                                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <select
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Semua Status</option>
                            <option value="menunggu">Menunggu Verifikasi</option>
                            <option value="terverifikasi">Terverifikasi</option>
                            <option value="ditolak">Ditolak</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Calon Siswa List -->
            <div class="space-y-4">
                <!-- Siswa Item 1 -->
                @foreach ($calon_siswa as $item)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-blue-600 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $item->nama_lengkap }}</h3>
                                    <p class="text-sm text-gray-600">NISN: {{ $item->nisn }} | Email: {{ $item->email }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full">
                                    {{ $item->status_pendaftaran }}
                                </span>
                                <button onclick="openVerifikasiModal(1)"
                                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-eye mr-2"></i>Verifikasi
                                </button>
                            </div>
                        </div>

                        <!-- Progress Berkas -->
                        @foreach ($calon_siswa as $item)
                            @foreach ($item->berkas as $berkas)
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-camera text-green-600"></i>
                                    <span class="text-sm text-gray-700">{{ $berkas->jenis_berkas }}</span>
                                    <i class="fas fa-check-circle text-green-600 text-xs"></i>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between mt-6">
                <div class="text-sm text-gray-700">
                    Menampilkan <span class="font-medium">1</span> sampai <span class="font-medium">3</span> dari <span
                        class="font-medium">48</span> calon siswa
                </div>
                <div class="flex items-center space-x-2">
                    <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-2 text-white bg-blue-600 border border-blue-600 rounded-lg">1</button>
                    <button
                        class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">2</button>
                    <button
                        class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">3</button>
                    <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Verifikasi Modal -->
    <div id="verifikasiModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeModal()"></div>

            <div
                class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Verifikasi Berkas - Ahmad Fauzi Rahman</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Berkas Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Pas Foto -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Pas Foto 3x4</h4>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Valid</span>
                        </div>
                        <div class="bg-gray-100 rounded-lg h-32 flex items-center justify-center mb-3">
                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                        </div>
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                <i class="fas fa-eye mr-1"></i>Lihat
                            </button>
                            <button class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                <i class="fas fa-check mr-1"></i>Terima
                            </button>
                        </div>
                    </div>

                    <!-- Kartu Keluarga -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">Kartu Keluarga</h4>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Review</span>
                        </div>
                        <div class="bg-gray-100 rounded-lg h-32 flex items-center justify-center mb-3">
                            <i class="fas fa-file-pdf text-gray-400 text-2xl"></i>
                        </div>
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                <i class="fas fa-eye mr-1"></i>Lihat
                            </button>
                            <button class="flex-1 px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                                <i class="fas fa-times mr-1"></i>Tolak
                            </button>
                        </div>
                    </div>

                    <!-- Add more berkas items here -->
                </div>

                <!-- Catatan -->
                <div class="mb-6">
                    <label for="catatan" class="block text-sm font-medium text-gray-700 mb-2">Catatan Verifikasi</label>
                    <textarea id="catatan" rows="3"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button onclick="closeModal()"
                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Batal
                    </button>
                    <button onclick="tolakBerkas()"
                        class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-times mr-2"></i>Tolak Semua
                    </button>
                    <button onclick="terimaBerkas()"
                        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-check mr-2"></i>Terima Semua
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        }

        function openVerifikasiModal(id) {
            document.getElementById('verifikasiModal').classList.remove('hidden');

            const grid = document.getElementById('berkasGrid');
            grid.innerHTML = `<p class="text-sm text-gray-500">Memuat berkas...</p>`;

            // Ambil data berkas dari endpoint Laravel
            fetch(`/operator/calon-siswa/${siswaId}/berkas`)
                .then(res => res.json())
                .then(data => {
                    let html = '';

                    data.forEach(item => {
                        html += `
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">${item.jenis_berkas}</h4>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">${item.status}</span>
                    </div>
                    <div class="bg-gray-100 rounded-lg h-32 flex items-center justify-center mb-3">
                        <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                    </div>
                    <div class="flex space-x-2">
                        <a href="${item.url}" target="_blank" class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                            <i class="fas fa-eye mr-1"></i>Lihat
                        </a>
                        <button class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                            <i class="fas fa-check mr-1"></i>Terima
                        </button>
                    </div>
                </div>`;
                    });

                    grid.innerHTML = html;
                });
        }

        function closeModal() {
            document.getElementById('verifikasiModal').classList.add('hidden');
        }

        function terimaBerkas() {
            if (confirm('Apakah Anda yakin ingin menerima semua berkas?')) {
                alert('Berkas berhasil diverifikasi!');
                closeModal();
            }
        }

        function tolakBerkas() {
            if (confirm('Apakah Anda yakin ingin menolak berkas ini?')) {
                alert('Berkas ditolak!');
                closeModal();
            }
        }
    </script>
</body>

</html>