<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\Auth;
use App\Models\CalonSiswa;

use Illuminate\Http\Request;

class CalonSiswaController extends Controller
{
    public function index()
    {
        return view('calon-siswa.data-diri');
    }

    public function create()
    {
        return view('calon-siswa.data-diri');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_lengkap' => 'required',
            'jenis_kelamin' => 'required',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required',
            'agama' => 'required',
            'nik' => 'required',
            'nisn' => 'required',
            'alamat' => 'required',
            'no_hp' => 'required',
            'email' => 'required',
            'hobi' => 'required',
            'cita_cita' => 'required',
            'sekolah_asal' => 'required',
        ]);

        if ($validated['jenis_kelamin'] == 'Laki-laki') {
            $validated['jenis_kelamin'] = 'L';
        } else {
            $validated['jenis_kelamin'] = 'P';
        }

        $akun_id = Auth::id();

        CalonSiswa::create([
            'akun_id' => $akun_id,
            'nama_lengkap' => $validated['nama_lengkap'],
            'jenis_kelamin' => $validated['jenis_kelamin'],
            'tempat_lahir' => $validated['tempat_lahir'],
            'tanggal_lahir' => $validated['tanggal_lahir'],
            'agama' => $validated['agama'],
            'nik' => $validated['nik'],
            'nisn' => $validated['nisn'],
            'alamat' => $validated['alamat'],
            'no_hp' => $validated['no_hp'],
            'email' => $validated['email'],
            'hobi' => $validated['hobi'],
            'cita_cita' => $validated['cita_cita'],
            'sekolah_asal' => $validated['sekolah_asal'],
        ]);

        return redirect()->route('calon-siswa.data-orangtua')->with('success', 'Data calon siswa berhasil disimpan.');
    }

    public function edit($id)
    {
        $calonSiswa = CalonSiswa::findorFail($id);
        return view('calon-siswa.edit', compact('calonSiswa'));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'nama_lengkap' => 'required',
            'jenis_kelamin' => 'required',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required',
            'agama' => 'required',
            'nik' => 'required',
            'nisn' => 'required',
            'alamat' => 'required',
            'no_hp' => 'required',
            'email' => 'required',
            'hobi' => 'required',
            'cita_cita' => 'required',
            'sekolah_asal' => 'required',
        ]);

        $calonSiswa = CalonSiswa::findorFail($id);
        $calonSiswa->update([
            'nama_lengkap' => $validated['nama_lengkap'],
            'jenis_kelamin' => $validated['jenis_kelamin'],
            'tempat_lahir' => $validated['tempat_lahir'],
            'tanggal_lahir' => $validated['tanggal_lahir'],
            'agama' => $validated['agama'],
            'nik' => $validated['nik'],
            'nisn' => $validated['nisn'],
            'alamat' => $validated['alamat'],
            'no_hp' => $validated['no_hp'],
            'email' => $validated['email'],
            'hobi' => $validated['hobi'],
            'cita_cita' => $validated['cita_cita'],
            'sekolah_asal' => $validated['sekolah_asal'],
        ]);

        return redirect()->route('calon-siswa.index')->with('success', 'Data calon siswa berhasil diperbarui.');
    }

    public function destroy($id)
    {
        $calonSiswa = CalonSiswa::findorFail($id);
        $calonSiswa->delete();
        return redirect()->route('calon-siswa.index')->with('success', 'Data calon siswa berhasil dihapus.');
    }
}
