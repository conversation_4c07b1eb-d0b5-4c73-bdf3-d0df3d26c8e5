<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\Auth;
use App\Models\CalonSiswa;

use Illuminate\Http\Request;

class CalonSiswaController extends Controller
{
    public function index()
    {
        return view('calon-siswa.data-diri');
    }

    public function create()
    {
        return view('calon-siswa.data-diri');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_lengkap' => 'required',
            'jenis_kelamin' => 'required',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required',
            'agama' => 'required',
            'anak_keberapa' => 'required',
            'jumlah_saudara' => 'required',
            'hobi' => 'required',
            'cita_cita' => 'required',
            'dusun' => 'required',
            'rt' => 'required',
            'rw' => 'required',
            'kelurahan' => 'required',
            'kecamatan' => 'required',
            'no_hp' => 'required',
            'tempat_tinggal_sekarang' => 'required',
            'asal_sekolah' => 'required',
            'status_sekolah' => 'required',
            'nisn' => 'required',
            'nomor_peserta_un' => 'required'
        ]);

        $akun_id = Auth::id();

        CalonSiswa::create([
            'akun_id' => $akun_id,
            'nama_lengkap' => $validated['nama_lengkap'],
            'jenis_kelamin' => $validated['jenis_kelamin'],
            'tempat_lahir' => $validated['tempat_lahir'],
            'tanggal_lahir' => $validated['tanggal_lahir'],
            'agama' => $validated['agama'],
            'anak_keberapa' => $validated['anak_keberapa'],
            'jumlah_saudara' => $validated['jumlah_saudara'],
            'hobi' => $validated['hobi'],
            'cita_cita' => $validated['cita_cita'],
            'dusun' => $validated['dusun'],
            'rt' => $validated['rt'],
            'rw' => $validated['rw'],
            'kelurahan' => $validated['kelurahan'],
            'kecamatan' => $validated['kecamatan'],
            'no_hp' => $validated['no_hp'],
            'tempat_tinggal_sekarang' => $validated['tempat_tinggal_sekarang'],
            'asal_sekolah' => $validated['asal_sekolah'],
            'status_sekolah' => $validated['status_sekolah'],
            'nisn' => $validated['nisn'],
            'nomor_peserta_un' => $validated['nomor_peserta_un'],
            'status_pendaftaran' => 'menunggu verifikasi'
        ]);

        return redirect()->route('calon-siswa.data-orangtua')->with('success', 'Data calon siswa berhasil disimpan.');
    }

    public function edit($id)
    {
        $calonSiswa = CalonSiswa::findorFail($id);
        return view('calon-siswa.edit', compact('calonSiswa'));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'nama_lengkap' => 'required',
            'jenis_kelamin' => 'required',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required',
            'agama' => 'required',
            'anak_keberapa' => 'required',
            'jumlah_saudara' => 'required',
            'hobi' => 'required',
            'cita_cita' => 'required',
            'dusun' => 'required',
            'rt' => 'required',
            'rw' => 'required',
            'kelurahan' => 'required',
            'kecamatan' => 'required',
            'no_hp' => 'required',
            'tempat_tinggal_sekarang' => 'required',
            'asal_sekolah' => 'required',
            'status_sekolah' => 'required',
            'nisn' => 'required',
            'nomor_peserta_un' => 'required'
        ]);


        $calonSiswa = CalonSiswa::findorFail($id);
        $calonSiswa->update([
            'nama_lengkap' => $validated['nama_lengkap'],
            'jenis_kelamin' => $validated['jenis_kelamin'],
            'tempat_lahir' => $validated['tempat_lahir'],
            'tanggal_lahir' => $validated['tanggal_lahir'],
            'agama' => $validated['agama'],
            'anak_keberapa' => $validated['anak_keberapa'],
            'jumlah_saudara' => $validated['jumlah_saudara'],
            'hobi' => $validated['hobi'],
            'cita_cita' => $validated['cita_cita'],
            'dusun' => $validated['dusun'],
            'rt' => $validated['rt'],
            'rw' => $validated['rw'],
            'kelurahan' => $validated['kelurahan'],
            'kecamanatan' => $validated['kecamatan'],
            'no_hp' => $validated['no_hp'],
            'tempat_tinggal_sekarang' => $validated['tempat_tinggal_sekarang'],
            'asal_sekolah' => $validated['asal_sekolah'],
            'status_sekolah' => $validated['status_sekolah'],
            'nisn' => $validated['nisn'],
            'nomor_peserta_un' => $validated['nomor_peserta_un']
        ]);

        return redirect()->route('calon-siswa.index')->with('success', 'Data calon siswa berhasil diperbarui.');
    }

    public function destroy($id)
    {
        $calonSiswa = CalonSiswa::findorFail($id);
        $calonSiswa->delete();
        return redirect()->route('calon-siswa.index')->with('success', 'Data calon siswa berhasil dihapus.');
    }
}
