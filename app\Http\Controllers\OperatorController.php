<?php

namespace App\Http\Controllers;
use App\Models\CalonSiswa;
use Illuminate\Http\Request;

class OperatorController extends Controller
{
    public function dashboard()
    {
        $jumlah_pendaftar = CalonSiswa::count();
        $jumlah_menunggu_verifikasi = CalonSiswa::where('status_pendaftaran', 'menunggu verifikasi')->count();
        $jumlah_lulus = CalonSiswa::where('status_pendaftaran', 'lulus')->count();
        return view('operator.dashboard', compact('jumlah_pendaftar', 'jumlah_menunggu_verifikasi', 'jumlah_lulus'));
    }
}
