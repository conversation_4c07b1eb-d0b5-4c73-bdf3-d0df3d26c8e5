<?php

namespace App\Http\Controllers;
use App\Models\Berkas;
use App\Models\CalonSiswa;
use Illuminate\Http\Request;

class VerifikasiController extends Controller
{
    public function index()
    {
        $calon_siswa = CalonSiswa::with('berkas')
            ->where('status_pendaftaran', 'menunggu_verifikasi')
            ->get();
        return view('operator.verifikasi', compact('calon_siswa'));
    }

    public function getBerkas($id)
    {
        $siswa = CalonSiswa::with('berkas')->findOrFail($id);

        $data = $siswa->berkas->map(function ($b) {
            return [
                'jenis_berkas' => $b->jenis_berkas,
                'status' => $b->status ?? 'Review',
                'url' => asset('storage/berkas/' . $b->file), // sesuaikan path
            ];
        });

        return response()->json($data);
    }
}
