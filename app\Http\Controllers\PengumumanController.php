<?php

namespace App\Http\Controllers;

use App\Models\Pengumuman;
use Illuminate\Http\Request;

class PengumumanController extends Controller
{
    public function index()
    {
        $pengumuman = Pengumuman::all();
        return view('operator.pengumuman', compact('pengumuman'));
    }

    public function create()
    {
        return view('pengumuman.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'judul' => 'required|max:255',
            'isi' => 'required|max:255',
            'status' => 'required|in:draft,aktif,nonaktif',
            'tanggal_publikasi' => 'required|date',
        ]);

        Pengumuman::create([
            'judul' => $validated['judul'],
            'isi' => $validated['isi'],
            'status' => $validated['status'],
            'tanggal_publikasi' => $validated['tanggal_publikasi'],
        ]);

        return redirect()->route('pengumuman.index')->with('success', 'Pengumuman berhasil dibuat!');
    }

    public function edit($id)
    {
        $pengumuman = Pengumuman::findorFail($id);
        return view('operator.pengumuman', compact('pengumuman'));
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'judul' => 'required|max:255',
            'isi' => 'required|max:255',
            'status' => 'required|in:draft,aktif,nonaktif',
            'tanggal_publikasi' => 'required|date',
        ]);

        Pengumuman::findorFail($id)->update([
            'judul' => $validated['judul'],
            'isi' => $validated['isi'],
            'status' => $validated['status'],
            'tanggal_publikasi' => $validated['tanggal_publikasi'],
        ]);

        return redirect()->route('pengumuman.index')->with('success', 'Pengumuman berhasil diupdate!');
    }

    public function destroy($id)
    {
        //
    }
}
